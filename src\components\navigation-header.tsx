'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Search, Menu, X, GraduationCap } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { SearchOverlay } from '@/components/search-overlay'

interface NavigationHeaderProps {
  showSearch?: boolean
  onSearch?: (query: string) => void
  searchQuery?: string
}

export function NavigationHeader({ showSearch = false, onSearch, searchQuery = '' }: NavigationHeaderProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchOverlayOpen, setSearchOverlayOpen] = useState(false)
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)

  // Update local search query when prop changes
  useEffect(() => {
    setLocalSearchQuery(searchQuery)
  }, [searchQuery])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch?.(localSearchQuery)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setLocalSearchQuery(value)
    // Don't call onSearch immediately - only on form submit
  }

  const navItems = [
    { name: 'Find Schools', href: '/showSchools' },
    { name: 'Add School', href: '/addSchool' },
  ]

  return (
    <header className="bg-gradient-to-r from-purple-600 to-purple-800 text-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
           <div className="text-2xl font-bold">
            <span className="text-white">School</span><span className="text-cyan-400">Connect</span>
          </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-white hover:text-purple-200 transition-colors duration-200 font-medium"
              >
                {item.name}
              </Link>
            ))}
            <button
              onClick={() => setSearchOverlayOpen(true)}
              className="text-white hover:text-purple-200 transition-colors duration-200 p-2"
            >
              <Search className="h-5 w-5" />
            </button>
          </nav>

          {/* Search Bar - Desktop */}
          {showSearch && (
            <div className="hidden md:flex items-center">
              <form onSubmit={handleSearch} className="flex items-center space-x-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    type="text"
                    placeholder="Search schools..."
                    value={localSearchQuery}
                    onChange={handleInputChange}
                    className="pl-10 pr-4 py-2 w-64 bg-white text-gray-900 border-0 focus:ring-2 focus:ring-purple-300"
                  />
                </div>
                <Button type="submit" className="bg-green-500 hover:bg-green-600 text-white">
                  Search
                </Button>
              </form>
            </div>
          )}

          {/* Mobile buttons */}
          <div className="flex items-center space-x-2 md:hidden">
            <button
              onClick={() => setSearchOverlayOpen(true)}
              className="text-white hover:text-purple-200 transition-colors duration-200 p-2"
            >
              <Search className="h-5 w-5" />
            </button>
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="text-white">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
            <SheetContent side="right" className="w-[280px] bg-white border-gray-200">
              <div className="flex flex-col h-full">
                {/* Mobile Logo */}
                <div className="flex items-center space-x-2 py-6 px-2 border-b border-gray-100">
                  <div className="text-xl font-bold">
                    <span className="text-gray-900">School</span><span className="text-purple-600">Connect</span>
                  </div>
                </div>

                {/* Mobile Navigation */}
                <nav className="flex flex-col py-4">
                  {navItems.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className="text-gray-700 hover:text-purple-600 hover:bg-purple-50 transition-all duration-200 font-medium py-3 px-4 rounded-lg mx-2 mb-1"
                      onClick={() => setIsOpen(false)}
                    >
                      {item.name}
                    </Link>
                  ))}
                </nav>

              </div>
            </SheetContent>
          </Sheet>
          </div>
        </div>
      </div>
      
      <SearchOverlay 
        isOpen={searchOverlayOpen} 
        onClose={() => setSearchOverlayOpen(false)} 
      />
    </header>
  )
}
